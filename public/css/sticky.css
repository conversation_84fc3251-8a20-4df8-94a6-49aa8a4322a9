/* 粘性元素样式 */

/* 添加跟随页面滚动的样式 */
.sticky-container {
  position: relative; /* 相对定位，作为参考点 */
}

.sticky-element {
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  /* 确保sticky元素在固定定位时不会覆盖弹出框 */
  z-index: 100;
}

/* 当元素固定时，保持原来的空间 */
.sticky-placeholder {
  display: none; /* 默认隐藏 */
}

/* 显示占位符，保持布局 */
.show-placeholder {
  display: block;
}

/* 右侧边栏粘性容器样式 */
.right-sidebar-sticky-container {
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  /* 确保不会覆盖弹出框 */
  z-index: 99 !important;
}

/* 右侧边栏粘性容器内的元素样式 */
.right-sidebar-sticky-container .float1,
.right-sidebar-sticky-container .float2,
.right-sidebar-sticky-container .news-ad {
  margin-bottom: 20px;
  width: 260px !important;
}

.right-sidebar-sticky-container .float1:last-child,
.right-sidebar-sticky-container .float2:last-child,
.right-sidebar-sticky-container .news-ad:last-child {
  margin-bottom: 0;
}

/* 确保粘性容器中的图片保持原有尺寸 */
.right-sidebar-sticky-container img {
  width: 260px !important;
  height: 140px !important;
  object-fit: cover;
}

/* 确保粘性容器中的元素布局正确 */
.right-sidebar-sticky-container .float1,
.right-sidebar-sticky-container .float2 {
  display: block;
  width: 260px !important;
}

.right-sidebar-sticky-container .news-ad {
  display: block;
  width: 260px !important;
  flex-shrink: 0;
}

/* 占位符样式 */
.sticky-placeholder-sidebar,
.sticky-placeholder-news-ad {
  visibility: hidden;
}
